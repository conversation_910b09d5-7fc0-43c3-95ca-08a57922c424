# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class RouterRoles(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = RouterRoles()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsRouterRoles(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # RouterRoles
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # RouterRoles
    def Broker(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from wamp.proto.BrokerFeatures import BrokerFeatures
            obj = BrokerFeatures()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # RouterRoles
    def Dealer(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from wamp.proto.DealerFeatures import DealerFeatures
            obj = DealerFeatures()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def RouterRolesStart(builder): builder.StartObject(2)
def Start(builder):
    return RouterRolesStart(builder)
def RouterRolesAddBroker(builder, broker): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(broker), 0)
def AddBroker(builder, broker):
    return RouterRolesAddBroker(builder, broker)
def RouterRolesAddDealer(builder, dealer): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(dealer), 0)
def AddDealer(builder, dealer):
    return RouterRolesAddDealer(builder, dealer)
def RouterRolesEnd(builder): return builder.EndObject()
def End(builder):
    return RouterRolesEnd(builder)