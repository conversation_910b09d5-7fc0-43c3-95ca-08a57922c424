# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class AuthScramWelcome(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthScramWelcome()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAuthScramWelcome(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # AuthScramWelcome
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AuthScramWelcome
    def Verifier(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def AuthScramWelcomeStart(builder): builder.StartObject(1)
def Start(builder):
    return AuthScramWelcomeStart(builder)
def AuthScramWelcomeAddVerifier(builder, verifier): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(verifier), 0)
def AddVerifier(builder, verifier):
    return AuthScramWelcomeAddVerifier(builder, verifier)
def AuthScramWelcomeEnd(builder): return builder.EndObject()
def End(builder):
    return AuthScramWelcomeEnd(builder)