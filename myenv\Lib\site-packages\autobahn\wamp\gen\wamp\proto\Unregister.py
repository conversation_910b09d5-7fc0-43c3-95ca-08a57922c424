# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Unregister(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Unregister()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsUnregister(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # Unregister
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Unregister
    def Session(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Unregister
    def Request(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # Unregister
    def Registration(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

def UnregisterStart(builder): builder.StartObject(3)
def Start(builder):
    return UnregisterStart(builder)
def UnregisterAddSession(builder, session): builder.PrependUint64Slot(0, session, 0)
def AddSession(builder, session):
    return UnregisterAddSession(builder, session)
def UnregisterAddRequest(builder, request): builder.PrependUint64Slot(1, request, 0)
def AddRequest(builder, request):
    return UnregisterAddRequest(builder, request)
def UnregisterAddRegistration(builder, registration): builder.PrependUint64Slot(2, registration, 0)
def AddRegistration(builder, registration):
    return UnregisterAddRegistration(builder, registration)
def UnregisterEnd(builder): return builder.EndObject()
def End(builder):
    return UnregisterEnd(builder)