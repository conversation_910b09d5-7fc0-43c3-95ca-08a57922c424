<!DOCTYPE html>
<html>
<head>
    <title>GeeksforGeeks</title>
</head>
<body>
    <h1>GeeksforGeeks</h1>
    <p id="data-container"></p>
    <script>
        console.log('ws://' + window.location.host + '/ws/somepath/');
        const socket = new WebSocket('ws://' + window.location.host + '/ws/somepath/');

        socket.onmessage = function(event) {
            const data = JSON.parse(event.data);
            console.log(data.message);
            document.getElementById("data-container").textContent = data.message;
        };
    </script>
</body>
</html>