{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://setuptools.pypa.io/en/latest/userguide/pyproject_config.html", "title": "``tool.setuptools`` table", "$$description": ["``setuptools``-specific configurations that can be set by users that require", "customization.", "These configurations are completely optional and probably can be skipped when", "creating simple packages. They are equivalent to some of the `Keywords", "<https://setuptools.pypa.io/en/latest/references/keywords.html>`_", "used by the ``setup.py`` file, and can be set via the ``tool.setuptools`` table.", "It considers only ``setuptools`` `parameters", "<https://setuptools.pypa.io/en/latest/userguide/pyproject_config.html#setuptools-specific-configuration>`_", "that are not covered by :pep:`621`; and intentionally excludes ``dependency_links``", "and ``setup_requires`` (incompatible with modern workflows/standards)."], "type": "object", "additionalProperties": false, "properties": {"platforms": {"type": "array", "items": {"type": "string"}}, "provides": {"$$description": ["Package and virtual package names contained within this package", "**(not supported by pip)**"], "type": "array", "items": {"type": "string", "format": "pep508-identifier"}}, "obsoletes": {"$$description": ["Packages which this package renders obsolete", "**(not supported by pip)**"], "type": "array", "items": {"type": "string", "format": "pep508-identifier"}}, "zip-safe": {"$$description": ["Whether the project can be safely installed and run from a zip file.", "**OBSOLETE**: only relevant for ``pkg_resources``, ``easy_install`` and", "``setup.py install`` in the context of ``eggs`` (**DEPRECATED**)."], "type": "boolean"}, "script-files": {"$$description": ["Legacy way of defining scripts (entry-points are preferred).", "Equivalent to the ``script`` keyword in ``setup.py``", "(it was renamed to avoid confusion with entry-point based ``project.scripts``", "defined in :pep:`621`).", "**DISCOURAGED**: generic script wrappers are tricky and may not work properly.", "Whenever possible, please use ``project.scripts`` instead."], "type": "array", "items": {"type": "string"}, "$comment": "TODO: is this field deprecated/should be removed?"}, "eager-resources": {"$$description": ["Resources that should be extracted together, if any of them is needed,", "or if any C extensions included in the project are imported.", "**OBSOLETE**: only relevant for ``pkg_resources``, ``easy_install`` and", "``setup.py install`` in the context of ``eggs`` (**DEPRECATED**)."], "type": "array", "items": {"type": "string"}}, "packages": {"$$description": ["Packages that should be included in the distribution.", "It can be given either as a list of package identifiers", "or as a ``dict``-like structure with a single key ``find``", "which corresponds to a dynamic call to", "``setuptools.config.expand.find_packages`` function.", "The ``find`` key is associated with a nested ``dict``-like structure that can", "contain ``where``, ``include``, ``exclude`` and ``namespaces`` keys,", "mimicking the keyword arguments of the associated function."], "oneOf": [{"title": "Array of Python package identifiers", "type": "array", "items": {"$ref": "#/definitions/package-name"}}, {"$ref": "#/definitions/find-directive"}]}, "package-dir": {"$$description": [":class:`dict`-like structure mapping from package names to directories where their", "code can be found.", "The empty string (as key) means that all packages are contained inside", "the given directory will be included in the distribution."], "type": "object", "additionalProperties": false, "propertyNames": {"anyOf": [{"const": ""}, {"$ref": "#/definitions/package-name"}]}, "patternProperties": {"^.*$": {"type": "string"}}}, "package-data": {"$$description": ["Mapping from package names to lists of glob patterns.", "Usually this option is not needed when using ``include-package-data = true``", "For more information on how to include data files, check ``setuptools`` `docs", "<https://setuptools.pypa.io/en/latest/userguide/datafiles.html>`_."], "type": "object", "additionalProperties": false, "propertyNames": {"anyOf": [{"type": "string", "format": "python-module-name"}, {"const": "*"}]}, "patternProperties": {"^.*$": {"type": "array", "items": {"type": "string"}}}}, "include-package-data": {"$$description": ["Automatically include any data files inside the package directories", "that are specified by ``MANIFEST.in``", "For more information on how to include data files, check ``setuptools`` `docs", "<https://setuptools.pypa.io/en/latest/userguide/datafiles.html>`_."], "type": "boolean"}, "exclude-package-data": {"$$description": ["Mapping from package names to lists of glob patterns that should be excluded", "For more information on how to include data files, check ``setuptools`` `docs", "<https://setuptools.pypa.io/en/latest/userguide/datafiles.html>`_."], "type": "object", "additionalProperties": false, "propertyNames": {"anyOf": [{"type": "string", "format": "python-module-name"}, {"const": "*"}]}, "patternProperties": {"^.*$": {"type": "array", "items": {"type": "string"}}}}, "namespace-packages": {"type": "array", "items": {"type": "string", "format": "python-module-name-relaxed"}, "$comment": "https://setuptools.pypa.io/en/latest/userguide/package_discovery.html", "description": "**DEPRECATED**: use implicit namespaces instead (:pep:`420`)."}, "py-modules": {"description": "Modules that setuptools will manipulate", "type": "array", "items": {"type": "string", "format": "python-module-name-relaxed"}, "$comment": "TODO: clarify the relationship with ``packages``"}, "ext-modules": {"description": "Extension modules to be compiled by setuptools", "type": "array", "items": {"$ref": "#/definitions/ext-module"}}, "data-files": {"$$description": ["``dict``-like structure where each key represents a directory and", "the value is a list of glob patterns that should be installed in them.", "**DISCOURAGED**: please notice this might not work as expected with wheels.", "Whenever possible, consider using data files inside the package directories", "(or create a new namespace package that only contains data files).", "See `data files support", "<https://setuptools.pypa.io/en/latest/userguide/datafiles.html>`_."], "type": "object", "patternProperties": {"^.*$": {"type": "array", "items": {"type": "string"}}}}, "cmdclass": {"$$description": ["Mapping of distutils-style command names to ``setuptools.Command`` subclasses", "which in turn should be represented by strings with a qualified class name", "(i.e., \"dotted\" form with module), e.g.::\n\n", "    cmdclass = {mycmd = \"pkg.subpkg.module.CommandClass\"}\n\n", "The command class should be a directly defined at the top-level of the", "containing module (no class nesting)."], "type": "object", "patternProperties": {"^.*$": {"type": "string", "format": "python-qualified-identifier"}}}, "license-files": {"type": "array", "items": {"type": "string"}, "$$description": ["**PROVISIONAL**: list of glob patterns for all license files being distributed.", "(likely to become standard with :pep:`639`).", "By default: ``['LICEN[CS]E*', 'COPYING*', 'NOTICE*', 'AUTHORS*']``"], "$comment": "TODO: revise if PEP 639 is accepted. Probably ``project.license-files``?"}, "dynamic": {"type": "object", "description": "Instructions for loading :pep:`621`-related metadata dynamically", "additionalProperties": false, "properties": {"version": {"$$description": ["A version dynamically loaded via either the ``attr:`` or ``file:``", "directives. Please make sure the given file or attribute respects :pep:`440`.", "Also ensure to set ``project.dynamic`` accordingly."], "oneOf": [{"$ref": "#/definitions/attr-directive"}, {"$ref": "#/definitions/file-directive"}]}, "classifiers": {"$ref": "#/definitions/file-directive"}, "description": {"$ref": "#/definitions/file-directive"}, "entry-points": {"$ref": "#/definitions/file-directive"}, "dependencies": {"$ref": "#/definitions/file-directive-for-dependencies"}, "optional-dependencies": {"type": "object", "propertyNames": {"type": "string", "format": "pep508-identifier"}, "additionalProperties": false, "patternProperties": {".+": {"$ref": "#/definitions/file-directive-for-dependencies"}}}, "readme": {"type": "object", "anyOf": [{"$ref": "#/definitions/file-directive"}, {"type": "object", "properties": {"content-type": {"type": "string"}, "file": {"$ref": "#/definitions/file-directive/properties/file"}}, "additionalProperties": false}], "required": ["file"]}}}}, "definitions": {"package-name": {"$id": "#/definitions/package-name", "title": "Valid package name", "description": "Valid package name (importable or :pep:`561`).", "type": "string", "anyOf": [{"type": "string", "format": "python-module-name-relaxed"}, {"type": "string", "format": "pep561-stub-name"}]}, "ext-module": {"$id": "#/definitions/ext-module", "title": "Extension module", "description": "Parameters to construct a :class:`setuptools.Extension` object", "type": "object", "required": ["name", "sources"], "additionalProperties": false, "properties": {"name": {"type": "string", "format": "python-module-name-relaxed"}, "sources": {"type": "array", "items": {"type": "string"}}, "include-dirs": {"type": "array", "items": {"type": "string"}}, "define-macros": {"type": "array", "items": {"type": "array", "items": [{"description": "macro name", "type": "string"}, {"description": "macro value", "oneOf": [{"type": "string"}, {"type": "null"}]}], "additionalItems": false}}, "undef-macros": {"type": "array", "items": {"type": "string"}}, "library-dirs": {"type": "array", "items": {"type": "string"}}, "libraries": {"type": "array", "items": {"type": "string"}}, "runtime-library-dirs": {"type": "array", "items": {"type": "string"}}, "extra-objects": {"type": "array", "items": {"type": "string"}}, "extra-compile-args": {"type": "array", "items": {"type": "string"}}, "extra-link-args": {"type": "array", "items": {"type": "string"}}, "export-symbols": {"type": "array", "items": {"type": "string"}}, "swig-opts": {"type": "array", "items": {"type": "string"}}, "depends": {"type": "array", "items": {"type": "string"}}, "language": {"type": "string"}, "optional": {"type": "boolean"}, "py-limited-api": {"type": "boolean"}}}, "file-directive": {"$id": "#/definitions/file-directive", "title": "'file:' directive", "description": "Value is read from a file (or list of files and then concatenated)", "type": "object", "additionalProperties": false, "properties": {"file": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["file"]}, "file-directive-for-dependencies": {"title": "'file:' directive for dependencies", "allOf": [{"$$description": ["**BETA**: subset of the ``requirements.txt`` format", "without ``pip`` flags and options", "(one :pep:`508`-compliant string per line,", "lines that are blank or start with ``#`` are excluded).", "See `dynamic metadata", "<https://setuptools.pypa.io/en/latest/userguide/pyproject_config.html#dynamic-metadata>`_."]}, {"$ref": "#/definitions/file-directive"}]}, "attr-directive": {"title": "'attr:' directive", "$id": "#/definitions/attr-directive", "$$description": ["Value is read from a module attribute. Supports callables and iterables;", "unsupported types are cast via ``str()``"], "type": "object", "additionalProperties": false, "properties": {"attr": {"type": "string", "format": "python-qualified-identifier"}}, "required": ["attr"]}, "find-directive": {"$id": "#/definitions/find-directive", "title": "'find:' directive", "type": "object", "additionalProperties": false, "properties": {"find": {"type": "object", "$$description": ["Dynamic `package discovery", "<https://setuptools.pypa.io/en/latest/userguide/package_discovery.html>`_."], "additionalProperties": false, "properties": {"where": {"description": "Directories to be searched for packages (Unix-style relative path)", "type": "array", "items": {"type": "string"}}, "exclude": {"type": "array", "$$description": ["Exclude packages that match the values listed in this field.", "Can container shell-style wildcards (e.g. ``'pkg.*'``)"], "items": {"type": "string"}}, "include": {"type": "array", "$$description": ["Restrict the found packages to just the ones listed in this field.", "Can container shell-style wildcards (e.g. ``'pkg.*'``)"], "items": {"type": "string"}}, "namespaces": {"type": "boolean", "$$description": ["When ``True``, directories without a ``__init__.py`` file will also", "be scanned for :pep:`420`-style implicit namespaces"]}}}}}}}